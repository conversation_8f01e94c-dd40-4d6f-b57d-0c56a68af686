# 胎儿Y染色体浓度多元线性回归分析 - 完整代码说明

## 📁 文件清单

### 主要分析文件
1. **`question_1.m`** - 完整的MATLAB分析代码（461行）
2. **`data_analysis.py`** - 完整的Python分析代码
3. **`results_visualization.py`** - Python结果可视化代码
4. **`test_matlab_code.m`** - MATLAB代码测试文件

### 报告文件
5. **`regression_analysis_report.md`** - 详细分析报告
6. **`完整代码说明.md`** - 本文件

### 生成的图表
7. **`exploratory_analysis.png`** - Python生成的探索性分析图表
8. **`regression_results.png`** - Python生成的回归结果图表
9. **`matlab_exploratory_analysis.png`** - MATLAB生成的探索性分析图表（运行后生成）
10. **`matlab_regression_results.png`** - MATLAB生成的回归结果图表（运行后生成）

## 🎯 分析目标

建立多元线性回归模型，以**胎儿Y染色体浓度**作为因变量，分析其与以下自变量的关系：
- 孕妇基本信息：检测孕周、BMI
- 测序质量指标：原始读段数、比对比例、重复读段比例、唯一比对读段数、GC含量、过滤比例
- 染色体Z值：13、18、21、X、Y号染色体的Z值
- 染色体GC含量：13、18、21号染色体的GC含量

## 📊 主要发现

### 模型性能
- **测试集R²**: 0.1578
- **训练集R²**: 0.1389
- **RMSE**: ~0.030
- **样本数量**: 1,082个

### 最重要的影响因素（按影响程度排序）
1. **18号染色体的Z值** (系数: -0.008047) - 负影响最大
2. **检测孕周** (系数: 0.007315) - 正影响最大  
3. **孕妇BMI** (系数: -0.006240) - 负影响
4. **Y染色体的Z值** (系数: 0.005260) - 正影响
5. **X染色体的Z值** (系数: -0.002947) - 负影响

### 临床意义
- 孕周越大，Y染色体浓度越高（符合胎儿发育规律）
- BMI越高，Y染色体浓度越低（可能与母体代谢相关）
- 18号染色体Z值是重要的质量控制指标

## 💻 MATLAB代码结构 (`question_1.m`)

### 第一部分：数据加载和预处理 (第8-57行)
```matlab
% 读取Excel文件
filename = '附件666666.xlsx';
data = readtable(filename);

% 选择分析变量
selected_vars = {'Y染色体浓度', '检测孕周', '孕妇BMI', ...};

% 处理缺失值
analysis_data = rmmissing(analysis_data);
```

### 第二部分：探索性数据分析 (第59-89行)
```matlab
% 基本统计信息
data_matrix = table2array(analysis_data);

% 相关性分析
correlation_matrix = corrcoef(data_matrix);
```

### 第三部分：数据可视化 (第91-152行)
```matlab
% 创建6个子图
% 1. Y染色体浓度分布
% 2. 检测孕周分布  
% 3. 孕妇BMI分布
% 4-5. 散点图
% 6. 相关性热力图
```

### 第四部分：多元线性回归建模 (第154-238行)
```matlab
% 数据标准化
X_normalized = (X - X_mean) ./ X_std;

% 训练测试集分割
train_idx = randperm(n, round(0.8 * n));

% 最小二乘法回归
beta = (X_train' * X_train) \ (X_train' * Y_train);

% 性能评估
R2_train = SSR_train / SST_train;
RMSE_train = sqrt(mean((Y_train - Y_train_pred).^2));
```

### 第五部分：模型诊断 (第240-311行)
```matlab
% 残差分析
train_residuals = Y_train - Y_train_pred;

% 正态性检验
[h_jb, p_jb] = jbtest(train_residuals);

% 异方差检验
[corr_coef, p_hetero] = corr(Y_train_pred, train_residuals.^2);

% VIF计算
vif_values = 1 / (1 - R2_temp);
```

### 第六部分：结果可视化 (第313-420行)
```matlab
% 创建12个子图的综合结果展示
% 包括：系数图、预测图、残差图、Q-Q图、散点图、总结
```

### 第七部分：总结 (第422-461行)
```matlab
% 输出主要结论和建议
```

## 🐍 Python代码结构

### `data_analysis.py` - 主分析文件
- 数据加载和清理
- 探索性数据分析
- 多元线性回归建模
- 模型诊断（正态性、异方差性、多重共线性检验）
- 结果输出

### `results_visualization.py` - 可视化文件
- 创建综合结果图表
- 12个子图展示所有关键结果

## 🔧 使用方法

### 运行MATLAB代码
```matlab
% 在MATLAB中运行
run('question_1.m')
```

### 运行Python代码
```bash
# 运行主分析
python data_analysis.py

# 运行可视化
python results_visualization.py
```

### 测试MATLAB代码
```matlab
% 运行测试文件验证代码正确性
run('test_matlab_code.m')
```

## 📈 输出结果

### 控制台输出
- 详细的分析过程和结果
- 模型性能指标
- 回归系数排序
- 模型诊断结果

### 图表文件
- 探索性分析图表
- 回归结果可视化
- 残差分析图
- 模型诊断图

### 分析报告
- 完整的Markdown格式报告
- 包含结论和建议

## ⚠️ 注意事项

1. **数据文件**: 确保`附件666666.xlsx`在同一目录下
2. **MATLAB版本**: 建议使用MATLAB R2018b或更高版本
3. **Python依赖**: 需要pandas, numpy, matplotlib, seaborn, sklearn, scipy, statsmodels
4. **中文显示**: 图表中的中文可能需要配置字体

## 🎯 模型局限性和改进建议

### 局限性
- 模型解释能力有限（R² ≈ 0.16）
- 存在轻微的异方差性
- 残差不完全符合正态分布

### 改进建议
1. 考虑非线性模型（多项式回归、随机森林等）
2. 探索变量间的交互作用
3. 进行变量变换（对数变换、Box-Cox变换）
4. 收集更多相关变量
5. 分层建模（按孕周段或BMI组）

## 📞 技术支持

如有问题，请检查：
1. 数据文件路径是否正确
2. 所需软件包是否已安装
3. 文件编码是否为UTF-8
4. MATLAB工具箱是否完整

---

**分析完成时间**: 2025年9月5日  
**分析师**: Alex (工程师) & David (数据分析师)  
**项目**: 胎儿Y染色体浓度影响因素分析
