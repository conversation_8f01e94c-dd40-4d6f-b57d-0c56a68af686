#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
胎儿Y染色体浓度多元线性回归分析结果可视化
作者: <PERSON> (数据分析师)
日期: 2025-09-05
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_model_results_visualization():
    """创建模型结果可视化"""
    print("=" * 60)
    print("创建模型结果可视化图表")
    print("=" * 60)
    
    # 重新加载数据和建立模型（为了获取结果）
    df = pd.read_excel('附件666666.xlsx')
    
    selected_columns = [
        'Y染色体浓度',  # 因变量
        '检测孕周',     # 孕周数
        '孕妇BMI',      # BMI
        '原始读段数',
        '在参考基因组上比对的比例',
        '重复读段的比例',
        '唯一比对的读段数  ',  # 注意这里有空格
        'GC含量',
        '13号染色体的Z值',
        '18号染色体的Z值',
        '21号染色体的Z值',
        'X染色体的Z值',
        'Y染色体的Z值',
        '13号染色体的GC含量',
        '18号染色体的GC含量',
        '21号染色体的GC含量',
        '被过滤掉读段数的比例'
    ]
    
    analysis_df = df[selected_columns].copy()
    analysis_df = analysis_df.rename(columns={'唯一比对的读段数  ': '唯一比对的读段数'})
    analysis_df = analysis_df.dropna()
    
    # 准备数据
    y = analysis_df['Y染色体浓度']
    X = analysis_df.drop('Y染色体浓度', axis=1)
    
    # 数据标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    X_scaled_df = pd.DataFrame(X_scaled, columns=X.columns)
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled_df, y, test_size=0.2, random_state=42
    )
    
    # 建立模型
    model = LinearRegression()
    model.fit(X_train, y_train)
    
    # 预测
    y_train_pred = model.predict(X_train)
    y_test_pred = model.predict(X_test)
    
    # 残差
    train_residuals = y_train - y_train_pred
    test_residuals = y_test - y_test_pred
    
    # 回归系数
    coefficients = pd.DataFrame({
        '变量': X.columns,
        '系数': model.coef_,
        '绝对值': np.abs(model.coef_)
    }).sort_values('绝对值', ascending=False)
    
    # 创建综合可视化图表
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 回归系数条形图
    plt.subplot(3, 4, 1)
    top_10_coef = coefficients.head(10)
    colors = ['red' if x < 0 else 'blue' for x in top_10_coef['系数']]
    bars = plt.barh(range(len(top_10_coef)), top_10_coef['系数'], color=colors, alpha=0.7)
    plt.yticks(range(len(top_10_coef)), top_10_coef['变量'], fontsize=8)
    plt.xlabel('回归系数')
    plt.title('前10个重要变量的回归系数')
    plt.axvline(x=0, color='black', linestyle='--', alpha=0.5)
    
    # 2. 实际值 vs 预测值 (训练集)
    plt.subplot(3, 4, 2)
    plt.scatter(y_train, y_train_pred, alpha=0.6, color='blue', s=20)
    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)
    plt.xlabel('实际Y染色体浓度')
    plt.ylabel('预测Y染色体浓度')
    plt.title(f'训练集: 实际值 vs 预测值\nR² = {r2_score(y_train, y_train_pred):.4f}')
    
    # 3. 实际值 vs 预测值 (测试集)
    plt.subplot(3, 4, 3)
    plt.scatter(y_test, y_test_pred, alpha=0.6, color='green', s=20)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('实际Y染色体浓度')
    plt.ylabel('预测Y染色体浓度')
    plt.title(f'测试集: 实际值 vs 预测值\nR² = {r2_score(y_test, y_test_pred):.4f}')
    
    # 4. 残差分布 (训练集)
    plt.subplot(3, 4, 4)
    plt.hist(train_residuals, bins=30, alpha=0.7, color='blue', edgecolor='black')
    plt.xlabel('残差')
    plt.ylabel('频数')
    plt.title('训练集残差分布')
    plt.axvline(x=0, color='red', linestyle='--')
    
    # 5. 残差分布 (测试集)
    plt.subplot(3, 4, 5)
    plt.hist(test_residuals, bins=20, alpha=0.7, color='green', edgecolor='black')
    plt.xlabel('残差')
    plt.ylabel('频数')
    plt.title('测试集残差分布')
    plt.axvline(x=0, color='red', linestyle='--')
    
    # 6. 残差 vs 预测值 (训练集)
    plt.subplot(3, 4, 6)
    plt.scatter(y_train_pred, train_residuals, alpha=0.6, color='blue', s=20)
    plt.xlabel('预测值')
    plt.ylabel('残差')
    plt.title('训练集: 残差 vs 预测值')
    plt.axhline(y=0, color='red', linestyle='--')
    
    # 7. 残差 vs 预测值 (测试集)
    plt.subplot(3, 4, 7)
    plt.scatter(y_test_pred, test_residuals, alpha=0.6, color='green', s=20)
    plt.xlabel('预测值')
    plt.ylabel('残差')
    plt.title('测试集: 残差 vs 预测值')
    plt.axhline(y=0, color='red', linestyle='--')
    
    # 8. Q-Q图 (正态性检验)
    from scipy import stats
    plt.subplot(3, 4, 8)
    stats.probplot(train_residuals, dist="norm", plot=plt)
    plt.title('训练集残差Q-Q图')
    
    # 9. 重要变量与Y染色体浓度的关系
    plt.subplot(3, 4, 9)
    plt.scatter(analysis_df['检测孕周'], analysis_df['Y染色体浓度'], alpha=0.6, color='purple', s=20)
    plt.xlabel('检测孕周')
    plt.ylabel('Y染色体浓度')
    plt.title('检测孕周 vs Y染色体浓度')
    
    # 10. BMI与Y染色体浓度的关系
    plt.subplot(3, 4, 10)
    plt.scatter(analysis_df['孕妇BMI'], analysis_df['Y染色体浓度'], alpha=0.6, color='orange', s=20)
    plt.xlabel('孕妇BMI')
    plt.ylabel('Y染色体浓度')
    plt.title('孕妇BMI vs Y染色体浓度')
    
    # 11. 18号染色体Z值与Y染色体浓度的关系
    plt.subplot(3, 4, 11)
    plt.scatter(analysis_df['18号染色体的Z值'], analysis_df['Y染色体浓度'], alpha=0.6, color='brown', s=20)
    plt.xlabel('18号染色体的Z值')
    plt.ylabel('Y染色体浓度')
    plt.title('18号染色体Z值 vs Y染色体浓度')
    
    # 12. 模型性能总结
    plt.subplot(3, 4, 12)
    plt.axis('off')
    
    # 计算性能指标
    train_r2 = r2_score(y_train, y_train_pred)
    test_r2 = r2_score(y_test, y_test_pred)
    
    summary_text = f"""
模型性能总结

训练集 R²: {train_r2:.4f}
测试集 R²: {test_r2:.4f}

样本数量: {len(analysis_df)}
变量数量: {len(X.columns)}

前3个重要变量:
1. {coefficients.iloc[0]['变量']}
   系数: {coefficients.iloc[0]['系数']:.6f}
   
2. {coefficients.iloc[1]['变量']}
   系数: {coefficients.iloc[1]['系数']:.6f}
   
3. {coefficients.iloc[2]['变量']}
   系数: {coefficients.iloc[2]['系数']:.6f}
"""
    
    plt.text(0.1, 0.9, summary_text, fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('regression_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("回归分析结果图表已保存为 'regression_results.png'")
    
    return coefficients, train_r2, test_r2

if __name__ == "__main__":
    coefficients, train_r2, test_r2 = create_model_results_visualization()
    
    print("\n" + "=" * 60)
    print("模型结果可视化完成！")
    print("=" * 60)
