#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
胎儿Y染色体浓度多元线性回归分析
作者: <PERSON> (数据分析师)
日期: 2025-09-05
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_clean_data():
    """加载和清理数据"""
    print("=" * 60)
    print("第一步：数据加载和预处理")
    print("=" * 60)
    
    # 读取数据
    df = pd.read_excel('附件666666.xlsx')
    print(f"原始数据形状: {df.shape}")
    
    # 选择相关列进行分析
    # 因变量：Y染色体浓度
    # 自变量：孕周数、BMI、原始读段数、在参考基因组上比对的比例、重复读段的比例、
    #        唯一比对的读段数、GC含量、13号染色体的Z值、18号染色体的Z值、21号染色体的Z值、
    #        X染色体的Z值、Y染色体的Z值、13号染色体的GC含量、18号染色体的GC含量、
    #        21号染色体的GC含量、被过滤掉读段数的比例
    
    selected_columns = [
        'Y染色体浓度',  # 因变量
        '检测孕周',     # 孕周数
        '孕妇BMI',      # BMI
        '原始读段数',
        '在参考基因组上比对的比例',
        '重复读段的比例',
        '唯一比对的读段数  ',  # 注意这里有空格
        'GC含量',
        '13号染色体的Z值',
        '18号染色体的Z值',
        '21号染色体的Z值',
        'X染色体的Z值',
        'Y染色体的Z值',
        '13号染色体的GC含量',
        '18号染色体的GC含量',
        '21号染色体的GC含量',
        '被过滤掉读段数的比例'
    ]
    
    # 提取相关列
    analysis_df = df[selected_columns].copy()

    # 重命名列名，去掉空格
    analysis_df = analysis_df.rename(columns={'唯一比对的读段数  ': '唯一比对的读段数'})
    
    # 检查缺失值
    print("\n缺失值统计:")
    missing_stats = analysis_df.isnull().sum()
    print(missing_stats[missing_stats > 0])
    
    # 删除含有缺失值的行
    analysis_df = analysis_df.dropna()
    print(f"\n清理后数据形状: {analysis_df.shape}")
    
    return analysis_df

def exploratory_data_analysis(df):
    """探索性数据分析"""
    print("\n" + "=" * 60)
    print("第二步：探索性数据分析")
    print("=" * 60)
    
    # 基本统计信息
    print("\n描述性统计:")
    print(df.describe())
    
    # 检查异常值（使用IQR方法）
    print("\n异常值检测（IQR方法）:")
    for col in df.columns:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        if len(outliers) > 0:
            print(f"{col}: {len(outliers)} 个异常值 ({len(outliers)/len(df)*100:.2f}%)")
    
    # 相关性分析
    print("\n相关性分析:")
    correlation_matrix = df.corr()
    y_correlations = correlation_matrix['Y染色体浓度'].sort_values(key=abs, ascending=False)
    print("\n与Y染色体浓度的相关系数（按绝对值排序）:")
    for var, corr in y_correlations.items():
        if var != 'Y染色体浓度':
            print(f"{var}: {corr:.4f}")
    
    return correlation_matrix

def create_visualizations(df, correlation_matrix):
    """创建可视化图表"""
    print("\n" + "=" * 60)
    print("第三步：数据可视化")
    print("=" * 60)
    
    # 设置图形大小
    plt.figure(figsize=(20, 15))
    
    # 1. Y染色体浓度分布
    plt.subplot(3, 4, 1)
    plt.hist(df['Y染色体浓度'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('Y染色体浓度分布')
    plt.xlabel('Y染色体浓度')
    plt.ylabel('频数')
    
    # 2. 孕周数分布
    plt.subplot(3, 4, 2)
    plt.hist(df['检测孕周'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
    plt.title('检测孕周分布')
    plt.xlabel('检测孕周')
    plt.ylabel('频数')
    
    # 3. BMI分布
    plt.subplot(3, 4, 3)
    plt.hist(df['孕妇BMI'], bins=25, alpha=0.7, color='salmon', edgecolor='black')
    plt.title('孕妇BMI分布')
    plt.xlabel('BMI')
    plt.ylabel('频数')
    
    # 4. Y染色体浓度 vs 检测孕周
    plt.subplot(3, 4, 4)
    plt.scatter(df['检测孕周'], df['Y染色体浓度'], alpha=0.6, color='blue')
    plt.title('Y染色体浓度 vs 检测孕周')
    plt.xlabel('检测孕周')
    plt.ylabel('Y染色体浓度')
    
    # 5. Y染色体浓度 vs BMI
    plt.subplot(3, 4, 5)
    plt.scatter(df['孕妇BMI'], df['Y染色体浓度'], alpha=0.6, color='red')
    plt.title('Y染色体浓度 vs 孕妇BMI')
    plt.xlabel('孕妇BMI')
    plt.ylabel('Y染色体浓度')
    
    # 6. Y染色体浓度 vs GC含量
    plt.subplot(3, 4, 6)
    plt.scatter(df['GC含量'], df['Y染色体浓度'], alpha=0.6, color='green')
    plt.title('Y染色体浓度 vs GC含量')
    plt.xlabel('GC含量')
    plt.ylabel('Y染色体浓度')
    
    # 7. Y染色体浓度 vs Y染色体Z值
    plt.subplot(3, 4, 7)
    plt.scatter(df['Y染色体的Z值'], df['Y染色体浓度'], alpha=0.6, color='purple')
    plt.title('Y染色体浓度 vs Y染色体Z值')
    plt.xlabel('Y染色体Z值')
    plt.ylabel('Y染色体浓度')
    
    # 8. 相关性热力图
    plt.subplot(3, 4, 8)
    # 选择与Y染色体浓度相关性较高的变量
    high_corr_vars = correlation_matrix['Y染色体浓度'].abs().sort_values(ascending=False).head(8).index
    corr_subset = correlation_matrix.loc[high_corr_vars, high_corr_vars]
    sns.heatmap(corr_subset, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.3f', cbar_kws={'shrink': 0.8})
    plt.title('高相关变量热力图')
    
    # 9-12. 箱线图检查异常值
    important_vars = ['Y染色体浓度', '检测孕周', '孕妇BMI', 'GC含量']
    for i, var in enumerate(important_vars, 9):
        plt.subplot(3, 4, i)
        plt.boxplot(df[var])
        plt.title(f'{var} 箱线图')
        plt.ylabel(var)
    
    plt.tight_layout()
    plt.savefig('exploratory_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形，不显示

    print("可视化图表已保存为 'exploratory_analysis.png'")

def build_regression_model(df):
    """建立多元线性回归模型"""
    print("\n" + "=" * 60)
    print("第四步：建立多元线性回归模型")
    print("=" * 60)

    # 准备数据
    # 因变量
    y = df['Y染色体浓度']

    # 自变量（去除因变量）
    X = df.drop('Y染色体浓度', axis=1)

    print(f"因变量: Y染色体浓度")
    print(f"自变量数量: {X.shape[1]}")
    print(f"样本数量: {X.shape[0]}")

    # 数据标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    X_scaled_df = pd.DataFrame(X_scaled, columns=X.columns)

    # 分割训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled_df, y, test_size=0.2, random_state=42
    )

    print(f"训练集大小: {X_train.shape[0]}")
    print(f"测试集大小: {X_test.shape[0]}")

    # 建立线性回归模型
    model = LinearRegression()
    model.fit(X_train, y_train)

    # 预测
    y_train_pred = model.predict(X_train)
    y_test_pred = model.predict(X_test)

    # 模型评估
    train_r2 = r2_score(y_train, y_train_pred)
    test_r2 = r2_score(y_test, y_test_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    train_mae = mean_absolute_error(y_train, y_train_pred)
    test_mae = mean_absolute_error(y_test, y_test_pred)

    print("\n模型性能评估:")
    print(f"训练集 R²: {train_r2:.4f}")
    print(f"测试集 R²: {test_r2:.4f}")
    print(f"训练集 RMSE: {train_rmse:.6f}")
    print(f"测试集 RMSE: {test_rmse:.6f}")
    print(f"训练集 MAE: {train_mae:.6f}")
    print(f"测试集 MAE: {test_mae:.6f}")

    # 回归系数分析
    print("\n回归系数分析:")
    coefficients = pd.DataFrame({
        '变量': X.columns,
        '系数': model.coef_,
        '绝对值': np.abs(model.coef_)
    }).sort_values('绝对值', ascending=False)

    print(f"截距项: {model.intercept_:.6f}")
    print("\n各变量的回归系数（按绝对值排序）:")
    for _, row in coefficients.iterrows():
        print(f"{row['变量']}: {row['系数']:.6f}")

    return model, scaler, X_train, X_test, y_train, y_test, y_train_pred, y_test_pred, coefficients

def model_diagnostics(model, X_train, X_test, y_train, y_test, y_train_pred, y_test_pred):
    """模型诊断和验证"""
    print("\n" + "=" * 60)
    print("第五步：模型诊断和验证")
    print("=" * 60)

    # 残差分析
    train_residuals = y_train - y_train_pred
    test_residuals = y_test - y_test_pred

    print("残差分析:")
    print(f"训练集残差均值: {train_residuals.mean():.8f}")
    print(f"训练集残差标准差: {train_residuals.std():.6f}")
    print(f"测试集残差均值: {test_residuals.mean():.8f}")
    print(f"测试集残差标准差: {test_residuals.std():.6f}")

    # 正态性检验（Shapiro-Wilk检验）
    from scipy.stats import shapiro

    # 由于样本量较大，随机抽取500个样本进行正态性检验
    if len(train_residuals) > 500:
        sample_residuals = np.random.choice(train_residuals, 500, replace=False)
    else:
        sample_residuals = train_residuals

    shapiro_stat, shapiro_p = shapiro(sample_residuals)
    print(f"\n残差正态性检验 (Shapiro-Wilk):")
    print(f"统计量: {shapiro_stat:.4f}, p值: {shapiro_p:.6f}")
    if shapiro_p > 0.05:
        print("残差符合正态分布 (p > 0.05)")
    else:
        print("残差不符合正态分布 (p ≤ 0.05)")

    # 异方差检验（Breusch-Pagan检验）
    from scipy.stats import pearsonr

    # 计算残差平方与预测值的相关性
    corr_coef, corr_p = pearsonr(y_train_pred, train_residuals**2)
    print(f"\n异方差检验:")
    print(f"残差平方与预测值的相关系数: {corr_coef:.4f}, p值: {corr_p:.6f}")
    if corr_p > 0.05:
        print("不存在异方差 (p > 0.05)")
    else:
        print("存在异方差 (p ≤ 0.05)")

    # 多重共线性检验（VIF）
    from statsmodels.stats.outliers_influence import variance_inflation_factor

    print(f"\n多重共线性检验 (VIF):")
    vif_data = pd.DataFrame()
    vif_data["变量"] = X_train.columns
    vif_data["VIF"] = [variance_inflation_factor(X_train.values, i) for i in range(X_train.shape[1])]
    vif_data = vif_data.sort_values('VIF', ascending=False)

    for _, row in vif_data.iterrows():
        vif_status = "严重共线性" if row['VIF'] > 10 else ("中等共线性" if row['VIF'] > 5 else "无共线性")
        print(f"{row['变量']}: {row['VIF']:.2f} ({vif_status})")

    return train_residuals, test_residuals, vif_data

if __name__ == "__main__":
    # 执行数据分析流程
    df = load_and_clean_data()
    correlation_matrix = exploratory_data_analysis(df)
    create_visualizations(df, correlation_matrix)

    # 建立回归模型
    model_results = build_regression_model(df)
    model, scaler, X_train, X_test, y_train, y_test, y_train_pred, y_test_pred, coefficients = model_results

    # 模型诊断
    diagnostics_results = model_diagnostics(model, X_train, X_test, y_train, y_test, y_train_pred, y_test_pred)
    train_residuals, test_residuals, vif_data = diagnostics_results

    print("\n" + "=" * 60)
    print("多元线性回归分析完成！")
    print("=" * 60)
