%% 测试MATLAB代码 - 简化版本
% 验证代码是否能正常运行

clear; clc; close all;

try
    % 测试数据读取
    fprintf('测试数据读取...\n');
    filename = '附件666666.xlsx';
    data = readtable(filename);
    fprintf('✓ 数据读取成功: %d行 x %d列\n', height(data), width(data));
    
    % 测试变量选择
    fprintf('测试变量选择...\n');
    selected_vars = {
        'Y染色体浓度',
        '检测孕周',
        '孕妇BMI',
        '原始读段数',
        '在参考基因组上比对的比例',
        '重复读段的比例',
        '唯一比对的读段数  ',
        'GC含量'
    };
    
    analysis_data = data(:, selected_vars);
    analysis_data = rmmissing(analysis_data);
    fprintf('✓ 变量选择成功: %d行 x %d列\n', height(analysis_data), width(analysis_data));
    
    % 测试数据转换
    fprintf('测试数据转换...\n');
    data_matrix = table2array(analysis_data);
    fprintf('✓ 数据转换成功\n');
    
    % 测试回归建模
    fprintf('测试回归建模...\n');
    Y = data_matrix(:, 1);
    X = data_matrix(:, 2:end);
    X_normalized = (X - mean(X)) ./ std(X);
    X_with_intercept = [ones(size(X_normalized, 1), 1), X_normalized];
    
    % 简单回归
    beta = (X_with_intercept' * X_with_intercept) \ (X_with_intercept' * Y);
    Y_pred = X_with_intercept * beta;
    
    % 计算R²
    SST = sum((Y - mean(Y)).^2);
    SSR = sum((Y_pred - mean(Y)).^2);
    R2 = SSR / SST;
    
    fprintf('✓ 回归建模成功, R² = %.4f\n', R2);
    
    % 测试图形生成
    fprintf('测试图形生成...\n');
    figure('Visible', 'off');  % 不显示图形
    subplot(2, 2, 1);
    histogram(Y, 20);
    title('Y染色体浓度分布');
    
    subplot(2, 2, 2);
    scatter(X(:, 1), Y, 20, 'filled');
    title('检测孕周 vs Y染色体浓度');
    
    subplot(2, 2, 3);
    scatter(Y, Y_pred, 20, 'filled');
    title('实际值 vs 预测值');
    
    subplot(2, 2, 4);
    residuals = Y - Y_pred;
    histogram(residuals, 20);
    title('残差分布');
    
    saveas(gcf, 'test_matlab_output.png');
    close(gcf);
    fprintf('✓ 图形生成成功\n');
    
    fprintf('\n=== 所有测试通过！MATLAB代码可以正常运行 ===\n');
    
catch ME
    fprintf('❌ 错误: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end
